[2025-08-11 00:42:27] production.ERROR: File does not exist at path D:\longtool\phpStudy_64\WWW\tool_api\php\api/database/migrations/2025_08_10_140000_fix_projects_table_structure.php. {"exception":"[object] (Illuminate\\Contracts\\Filesystem\\FileNotFoundException(code: 0): File does not exist at path D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api/database/migrations/2025_08_10_140000_fix_projects_table_structure.php. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\filesystem\\Filesystem.php:152)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(574): Illuminate\\Filesystem\\Filesystem->requireOnce('D:\\\\longtool\\\\php...')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(117): Illuminate\\Database\\Migrations\\Migrator->requireFiles(Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 {main}
"} 
[2025-08-11 00:47:02] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-11 08:41:25] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:27] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:28] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:29] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:31] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:32] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:32] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:33] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:34] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:56] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:41:59] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:42:40] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:42:42] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:42] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:44] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:45] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:46] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:47] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:48] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:50] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:51] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:51] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:50:52] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:50] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:51] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:52] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:53] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:54] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:55] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:55] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:57] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:52:57] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:54:28] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:54:29] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:56:17] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:56:18] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:58:21] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 08:58:22] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 09:00:10] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 09:00:40] production.ERROR: Static route "/py-api/storyboard-actions/search" is shadowed by previously defined variable route "/py\-api/storyboard\-actions/([^/]+)" for method "GET" {"exception":"[object] (FastRoute\\BadRouteException(code: 0): Static route \"/py-api/storyboard-actions/search\" is shadowed by previously defined variable route \"/py\\-api/storyboard\\-actions/([^/]+)\" for method \"GET\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php:95)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\DataGenerator\\RegexBasedAbstract.php(30): FastRoute\\DataGenerator\\RegexBasedAbstract->addStaticRoute('GET', Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\RouteCollector.php(44): FastRoute\\DataGenerator\\RegexBasedAbstract->addRoute('GET', Array, Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(209): FastRoute\\RouteCollector->addRoute('GET', '/py-api/storybo...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\nikic\\fast-route\\src\\functions.php(25): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(FastRoute\\RouteCollector))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(211): FastRoute\\simpleDispatcher(Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(175): Laravel\\Lumen\\Application->createDispatcher()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#15 {main}
"} 
[2025-08-11 10:05:38] production.INFO: WebSocket测试1 - 基础响应测试 {"method":"POST","url":"https://api.tiptop.cn/py-api/websocket/test1","headers":{"connection":["Keep-Alive"],"expect":["100-continue"],"content-length":["53"],"host":["api.tiptop.cn"],"user-agent":["Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.1591"],"content-type":["application/json"],"authorization":["Bearer vVP8UApdmXoU9B2z1jMtNMbrV8bGiP76w0ocpm7a1mZyY"]},"all_data":[]} 
[2025-08-11 10:06:01] production.INFO: WebSocket测试2 - Token提取测试开始  
[2025-08-11 10:06:01] production.INFO: Token提取结果 {"token":"vVP8UApdmXoU9B2z1jMtNMbrV8bGiP76w0ocpm7a1mZyY","authorization_header":"Bearer vVP8UApdmXoU9B2z1jMtNMbrV8bGiP76w0ocpm7a1mZyY","token_param":null} 
[2025-08-11 10:06:18] production.INFO: WebSocket测试3 - 认证服务测试开始  
[2025-08-11 10:06:18] production.INFO: 认证服务结果 {"success":true,"user_id":13,"response":null} 
[2025-08-11 10:06:35] production.INFO: WebSocket测试4 - 参数验证测试开始  
[2025-08-11 10:06:35] production.INFO: 参数获取结果 {"client_type":null,"business_type":null,"client_info":[],"all_params":[]} 
[2025-08-11 10:06:35] production.ERROR: 参数验证测试异常 {"error":"客户端类型不能为空","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\WebSocketTestController.php(145): App\\Http\\Controllers\\Controller->validateData(Array, Array, Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\WebSocketTestController->test4(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\WebSocketTestController), 'test4', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}"} 
[2025-08-11 10:08:06] production.INFO: WebSocket测试1 - 基础响应测试 {"method":"POST","url":"https://api.tiptop.cn/py-api/websocket/test1","headers":{"connection":["Keep-Alive"],"expect":["100-continue"],"content-length":["53"],"host":["api.tiptop.cn"],"user-agent":["Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.1591"],"content-type":["application/json"],"authorization":["Bearer vVP8UApdmXoU9B2z1jMtNMbrV8bGiP76w0ocpm7a1mZyY"]},"all_data":[],"content_type":"application/json","raw_content":"{\" client_type\\:\\python_tool\\,\\business_type\\:\\text\\}","json_data":[]} 
[2025-08-11 11:04:05] production.ERROR: Undefined constant "App\Http\Controllers\PyApi\request" {"exception":"[object] (Error(code: 0): Undefined constant \"App\\Http\\Controllers\\PyApi\\request\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\WebSocketController.php:59)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\WebSocketController->authenticate(Object(Laravel\\Lumen\\Http\\Request))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\WebSocketController), 'authenticate', Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#20 {main}
"} 
[2025-08-11 11:04:17] production.ERROR: Undefined constant "App\Http\Controllers\PyApi\request" {"exception":"[object] (Error(code: 0): Undefined constant \"App\\Http\\Controllers\\PyApi\\request\" at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\WebSocketController.php:59)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\WebSocketController->authenticate(Object(Laravel\\Lumen\\Http\\Request))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\WebSocketController), 'authenticate', Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#20 {main}
"} 
[2025-08-11 11:11:24] production.ERROR: Value of type null is not callable {"exception":"[object] (Error(code: 0): Value of type null is not callable at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php:77)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\WebSocketController.php(95): App\\Services\\PyApi\\WebSocketService->authenticateConnection(3, 'python_tool', 'text', '127.0.0.1', 'Mozilla/5.0 (Wi...', NULL, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\WebSocketController->authenticate(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\WebSocketController), 'authenticate', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}
"} 
[2025-08-11 11:12:16] production.ERROR: Value of type null is not callable {"exception":"[object] (Error(code: 0): Value of type null is not callable at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php:77)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\WebSocketController.php(95): App\\Services\\PyApi\\WebSocketService->authenticateConnection(3, 'python_tool', 'text', '127.0.0.1', 'Mozilla/5.0 (Wi...', NULL, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\WebSocketController->authenticate(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\WebSocketController), 'authenticate', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(18): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}
"} 
[2025-08-11 11:18:58] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":3,"session_id":"ws_GV5FxPlFYVAdHnUOyFi1Qtwh9qw5bMY5"} 
[2025-08-11 11:18:58] production.INFO: WebSocket连接认证成功 {"session_id":"ws_GV5FxPlFYVAdHnUOyFi1Qtwh9qw5bMY5","user_id":3,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-11 11:19:15] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":3,"session_id":"ws_qCRXsGpWvyXlyZWCYIHtzkdOdwIMMyrE"} 
[2025-08-11 11:19:15] production.INFO: WebSocket连接认证成功 {"session_id":"ws_qCRXsGpWvyXlyZWCYIHtzkdOdwIMMyrE","user_id":3,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-11 11:19:17] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":3,"session_id":"ws_dIr5OmI183nqKBOGghp134PeH5AplAEb"} 
[2025-08-11 11:19:17] production.INFO: WebSocket连接认证成功 {"session_id":"ws_dIr5OmI183nqKBOGghp134PeH5AplAEb","user_id":3,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
