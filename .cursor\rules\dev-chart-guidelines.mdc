---
description: AI视频创作工具系统图表集合 - 包含所有系统架构图、业务流程图和技术架构图 
globs:
alwaysApply: true
---

# AI视频创作工具系统图表集合

## 📊 系统架构图表

### 📊 完整系统架构图（环境切换优化版）

```mermaid
graph TB
    subgraph "用户层 - 视频创作工具"
        A[Py视频创作工具<br/>完整创作功能<br/>客户端视频编辑]
        B[WEB网页工具<br/>✅展示职责：首页工具展示、功能介绍、价格方案<br/>✅用户中心：注册登录、充值积分、积分明细、代理推广、代理结算<br/>✅作品广场：作品展示浏览、分类筛选、搜索查看、作品详情展示<br/>✅响应式设计：PC端、移动端、Py视频创作工具嵌入<br/>❌禁止：视频创作、AI生成、WebSocket通信、作品发布创建]
        M[管理后台<br/>@php/backend/<br/>基于Laravel 10<br/>✅系统配置管理：AI平台配置、系统参数设置<br/>✅用户管理：用户信息、权限管理、积分管理<br/>✅数据统计：业务数据分析、系统监控<br/>✅内容审核：作品审核、资源管理]
    end

    subgraph "业务服务层"
        C[工具API接口服务<br/>@php/api/<br/>基于Lumen 10.x<br/>统一API接口<br/>🚨环境切换机制实现层]
        C1[WebSocket服务<br/>swoole-cli artisan websocket:serve<br/>仅为Py视频创作工具提供实时通信]
        C2[AI资源管理服务<br/>资源生成+版本控制+本地导出<br/>可选：作品发布+审核系统]
    end

    subgraph "环境切换服务客户端层"
        SC1[AiServiceClient<br/>AI服务环境切换<br/>mock/real模式自动切换]
        SC2[ThirdPartyServiceClient<br/>第三方服务环境切换<br/>mock/real模式自动切换]
    end

    subgraph "开发支持层（模拟服务）"
        D[AI服务模拟<br/>@php/aiapi/<br/>本地开发模拟真实AI平台<br/>仅负责模拟，不包含环境切换]
        D2[第三方服务模拟<br/>@php/thirdapi/<br/>模拟微信、支付宝等<br/>仅负责模拟，不包含环境切换]
    end

    subgraph "数据存储层"
        E[MySQL数据库<br/>ai_tool<br/>主存储+事务保证<br/>+AI资源表+版本表+作品广场表]
        F[Redis缓存<br/>WebSocket会话管理<br/>快速查询+状态同步<br/>+资源状态缓存]
    end

    subgraph "真实AI服务（生产环境）"
        G[DeepSeek API<br/>剧情生成]
        H[LiblibAI API<br/>图像生成]
        I[KlingAI API<br/>视频生成]
        J[MiniMax API<br/>语音处理]
        K[火山引擎豆包 API<br/>专业语音AI]
    end

    subgraph "真实第三方服务（生产环境）"
        TP1[微信 API<br/>OAuth认证]
        TP2[支付宝 API<br/>支付服务]
        TP3[短信服务 API<br/>验证码发送]
    end

    %% HTTP API 连接 (蓝色虚线) - 三个工具都使用
    A -.->|🔵 HTTP API<br/>创作功能调用| C
    B -.->|🔵 HTTP API<br/>展示功能+用户中心功能+作品广场功能<br/>❌禁用WebSocket| C
    M -.->|🔵 HTTP API<br/>管理功能调用<br/>AdminApi控制器| C

    %% WebSocket 连接 (绿色粗线) - 仅Py视频创作工具使用
    A ==>|🟢 WebSocket实时通信<br/>AI生成进度推送<br/>任务状态通知| C1

    %% 服务间调用 (红色线) - 避免循环依赖
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC1
    C -->|🔴 业务逻辑调用<br/>通过服务客户端| SC2
    C -->|🔴 资源管理调用<br/>版本控制+审核| C2
    C1 -->|🔴 获取API密钥<br/>安全传输| C
    C2 -->|🔴 AI生成调用<br/>资源创建| SC1

    %% 环境切换调用 (紫色线) - 核心机制
    SC1 -->|🟣 开发环境<br/>mock模式| D
    SC1 -->|🟣 生产环境<br/>real模式| G
    SC1 -->|🟣 生产环境<br/>real模式| H
    SC1 -->|🟣 生产环境<br/>real模式| I
    SC1 -->|🟣 生产环境<br/>real模式| J
    SC1 -->|🟣 生产环境<br/>real模式| K
    SC2 -->|🟣 开发环境<br/>mock模式| D2
    SC2 -->|🟣 生产环境<br/>real模式| TP1
    SC2 -->|🟣 生产环境<br/>real模式| TP2
    SC2 -->|🟣 生产环境<br/>real模式| TP3

    %% 数据库连接 (橙色线) - 双重保障
    C -->|🟠 数据存储<br/>事务保证| E
    C -->|🟠 缓存操作<br/>快速查询| F
    C1 -->|🟠 会话管理<br/>状态同步| F
    C2 -->|🟠 资源数据存储<br/>版本管理| E
    C2 -->|🟠 资源状态缓存<br/>快速查询| F

    %% 节点样式 - 高对比度清晰字体
    classDef userLayer fill:#FFFFFF,stroke:#1976D2,stroke-width:3px,color:#000000
    classDef adminLayer fill:#FFFFFF,stroke:#FF9800,stroke-width:3px,color:#000000
    classDef serviceLayer fill:#FFFFFF,stroke:#7B1FA2,stroke-width:3px,color:#000000
    classDef clientLayer fill:#FFFFFF,stroke:#E91E63,stroke-width:3px,color:#000000
    classDef mockLayer fill:#FFFFFF,stroke:#4CAF50,stroke-width:3px,color:#000000
    classDef dataLayer fill:#FFFFFF,stroke:#F57C00,stroke-width:3px,color:#000000
    classDef realAiLayer fill:#FFFFFF,stroke:#388E3C,stroke-width:3px,color:#000000
    classDef realThirdLayer fill:#FFFFFF,stroke:#795548,stroke-width:3px,color:#000000

    class A,B userLayer
    class M adminLayer
    class C,C1,C2 serviceLayer
    class SC1,SC2 clientLayer
    class D,D2 mockLayer
    class E,F dataLayer
    class G,H,I,J,K realAiLayer
    class TP1,TP2,TP3 realThirdLayer
```

### 📊 AI服务集成模拟机制架构图

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Py视频创作工具] --> B[工具API接口服务]
        B --> E[AI服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[DeepSeek API格式模拟<br/>剧情生成/角色生成]
        E -.->|仅模拟，不真实调用| F2[LiblibAI API格式模拟<br/>图像生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F3[KlingAI API格式模拟<br/>图像生成/视频生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F4[MiniMax API格式模拟<br/>全业务支持]
        E -.->|仅模拟，不真实调用| F5[火山引擎豆包 API格式模拟<br/>语音合成/音效生成/音色生成]

        B --> T[第三方服务集成模拟返回数据服务]
        T -.->|仅模拟，不真实调用| G1[微信服务API格式模拟<br/>OAuth登录/微信支付]
        T -.->|仅模拟，不真实调用| G2[支付宝API格式模拟<br/>统一收单/退款查询]
        T -.->|仅模拟，不真实调用| G3[短信服务API格式模拟<br/>阿里云/腾讯云短信]
        T -.->|仅模拟，不真实调用| G4[邮件服务API格式模拟<br/>SMTP/SendCloud]
    end

    subgraph "生产环境"
        A2[Py视频创作工具] --> B2[工具API接口服务]
        B2 --> F6[真实第三方AI平台<br/>DeepSeek/LiblibAI/KlingAI<br/>MiniMax/火山引擎豆包]
        B2 --> G5[真实第三方服务平台<br/>微信/支付宝/阿里云/腾讯云]
    end

    style E fill:#fce4ec,stroke:#e91e63
    style T fill:#fff8e1,stroke:#ff9800
    style F1 fill:#ffebee
    style F2 fill:#ffebee
    style F3 fill:#ffebee
    style F4 fill:#ffebee
    style F5 fill:#ffebee
    style G1 fill:#ffebee
    style G2 fill:#ffebee
    style G3 fill:#ffebee
    style G4 fill:#ffebee
    style F6 fill:#e8f5e8,stroke:#4caf50
    style G5 fill:#e8f5e8,stroke:#4caf50
```

### 📊 AI服务调用流程对比图

```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant API as 工具API接口服务
    participant Mock as AI模拟服务
    participant Real as 真实AI平台

    Note over P,Mock: 本地开发阶段
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Mock: 真实调用AI平台格式接口<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Note over Mock: ✅ 唯一模拟职责<br/>1. 按对应AI平台要求验证参数<br/>2. 模拟对应平台响应状态
    alt 参数验证失败
        Mock->>API: 返回对应AI平台格式参数错误
    else 参数验证通过
        Mock->>API: 模拟成功/失败/超时状态
    end
    API->>P: 透明传递模拟结果

    Note over P,Real: 生产环境
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Real: 真实调用对应AI平台<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Real->>API: 返回真实结果
    API->>P: 透明传递真实结果
```

### 📊 项目依赖关系图

```mermaid
graph TB
    subgraph "本地开发环境"
        A[Py视频创作工具] --> B[工具API接口服务]
        C[WEB网页工具] --> B
        D[管理后台] --> B
        B --> E[AI服务集成模拟返回数据服务]
        B --> T[第三方服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[AI平台API格式模拟]
        T -.->|仅模拟，不真实调用| G1[第三方服务API格式模拟]
    end

    subgraph "生产环境"
        A2[Py视频创作工具] --> B2[工具API接口服务]
        C2[WEB网页工具] --> B2
        D2[管理后台] --> B2
        B2 --> F2[真实第三方AI平台]
        B2 --> G2[真实第三方服务平台]
    end

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style T fill:#fff8e1
    style F1 fill:#ffebee
    style G1 fill:#ffebee
    style F2 fill:#e8f5e8
    style G2 fill:#e8f5e8
```

#### 📋 依赖关系说明

**依赖说明**：
- **工具API接口服务**: 依赖"AI服务集成模拟返回数据服务"和"第三方服务集成模拟返回数据服务"提供API接口支持本地开发
- **Py视频创作工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **WEB网页工具**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发
- **管理后台**: 依赖"工具API接口服务"项目提供API接口实现所有功能的开发

**环境切换机制**：
- **本地开发环境**: 通过模拟服务提供完整的开发支持，无需真实第三方平台
- **生产环境**: 直接连接真实的AI平台和第三方服务，提供完整的生产功能
- **切换方式**: 通过配置文件控制，开发和生产环境无缝切换

## 🔄 Py视频创作工具业务流程图

### 🔄 用户管理业务流程

#### Py视频创作工具业务流程A-1: 用户注册流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    P->>A: 用户注册请求(用户名/邮箱/密码)
    A->>DB: 检查用户名/邮箱是否存在
    alt 用户已存在
        DB->>A: 返回用户已存在
        A->>P: 返回注册失败(用户已存在)
    else 用户不存在
        A->>A: 密码加密处理
        A->>DB: 创建新用户记录(状态:待验证)
        A->>TP: 发送验证邮件/短信
        TP->>A: 返回发送结果
        A->>R: 缓存验证码(5分钟过期)
        A->>P: 返回注册成功(待验证)

        Note over P: 用户输入验证码
        P->>A: 验证请求(验证码)
        A->>R: 验证验证码有效性
        alt 验证码有效
            A->>DB: 激活用户账户(状态:正常)
            A->>R: 清除验证码缓存
            A->>P: 返回验证成功
        else 验证码无效或过期
            A->>P: 返回验证失败(重新发送)
        end
    end
```

#### Py视频创作工具业务流程A-2: 用户登录流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 用户登录请求(用户名/密码)
    A->>DB: 查询用户信息
    alt 用户不存在
        A->>P: 返回登录失败(用户不存在)
    else 用户存在
        A->>A: 验证密码正确性
        alt 密码错误
            A->>DB: 记录登录失败次数
            A->>P: 返回登录失败(密码错误)
        else 密码正确
            A->>DB: 检查用户状态
            alt 用户被禁用
                A->>P: 返回登录失败(账户被禁用)
            else 用户状态正常
                A->>A: 生成自定义 Token
                A->>R: 存储Token(24小时过期)
                A->>DB: 更新最后登录时间
                A->>DB: 清除登录失败次数
                A->>P: 返回登录成功(Token+用户信息)
            end
        end
    end
```

#### Py视频创作工具业务流程A-3: Token验证流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant R as Redis缓存
    participant DB as MySQL数据库

    P->>A: API请求(携带Token)
    A->>A: 解析自定义 Token
    alt Token格式无效
        A->>P: 返回认证失败(Token无效)
    else Token格式有效
        A->>R: 检查Token是否在黑名单
        alt Token在黑名单
            A->>P: 返回认证失败(Token已失效)
        else Token有效
            A->>A: 验证Token签名和过期时间
            alt Token过期或签名无效
                A->>P: 返回认证失败(Token过期)
            else Token验证通过
                A->>DB: 查询用户当前状态
                alt 用户被禁用
                    A->>R: 将Token加入黑名单
                    A->>P: 返回认证失败(账户被禁用)
                else 用户状态正常
                    A->>A: 继续处理业务请求
                    Note over A: 执行具体的业务逻辑
                end
            end
        end
    end
```

#### Py视频创作工具业务流程A-4: 密码修改流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    Note over P: 用户已登录状态
    P->>A: 密码修改请求(旧密码/新密码/Token)
    A->>A: 验证Token有效性
    A->>DB: 查询用户信息
    A->>A: 验证旧密码正确性
    alt 旧密码错误
        A->>P: 返回修改失败(旧密码错误)
    else 旧密码正确
        A->>A: 验证新密码强度
        alt 新密码强度不足
            A->>P: 返回修改失败(密码强度不足)
        else 新密码符合要求
            A->>A: 加密新密码
            A->>DB: 更新用户密码
            A->>R: 将当前用户所有Token加入黑名单
            A->>TP: 发送密码修改通知邮件
            A->>P: 返回修改成功(需重新登录)
        end
    end
```

#### Py视频创作工具业务流程A-5: 忘记密码重置流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    P->>A: 忘记密码请求(邮箱)
    A->>DB: 查询邮箱对应用户
    alt 邮箱不存在
        A->>P: 返回邮箱不存在
    else 邮箱存在
        A->>A: 生成重置Token
        A->>R: 缓存重置Token(30分钟过期)
        A->>TP: 发送重置密码邮件(含重置链接)
        A->>P: 返回邮件发送成功

        Note over P: 用户点击邮件中的重置链接
        P->>A: 密码重置请求(重置Token/新密码)
        A->>R: 验证重置Token有效性
        alt Token无效或过期
            A->>P: 返回重置失败(链接已过期)
        else Token有效
            A->>A: 验证新密码强度
            A->>A: 加密新密码
            A->>DB: 更新用户密码
            A->>R: 清除重置Token
            A->>R: 将用户所有Token加入黑名单
            A->>TP: 发送密码重置成功通知
            A->>P: 返回重置成功(需重新登录)
        end
    end
```

### 🔄 业务功能流程

#### Py视频创作工具业务流程B-1: 充值积分流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    P->>A: 发起充值请求(金额/支付方式/Token)
    A->>A: 验证Token和充值参数
    A->>DB: 创建充值订单记录
    A->>TP: 调用支付接口(微信/支付宝)
    TP->>A: 返回支付二维码/链接
    A->>P: 返回支付信息

    Note over P: 用户完成支付
    TP->>A: 支付成功回调通知
    A->>A: 验证支付回调签名
    A->>DB: 查询订单状态
    alt 订单已处理
        A->>TP: 返回重复通知
    else 订单未处理
        A->>DB: 更新订单状态为已支付
        A->>DB: 增加用户积分余额
        A->>DB: 创建积分明细记录
        A->>R: 更新用户积分缓存
        A->>P: 推送充值成功通知
        A->>TP: 返回处理成功
    end
```

#### Py视频创作工具业务流程B-2: 积分管理流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 查询积分信息(Token)
    A->>A: 验证Token有效性
    A->>R: 检查积分缓存
    alt 缓存命中
        R->>A: 返回缓存的积分信息
    else 缓存未命中
        A->>DB: 查询用户积分余额
        A->>DB: 查询积分明细记录
        A->>R: 更新积分缓存
    end
    A->>P: 返回积分信息(余额/明细)

    Note over P: 用户请求积分明细
    P->>A: 查询积分明细(分页参数/Token)
    A->>DB: 分页查询积分明细
    A->>P: 返回积分明细列表(收入/支出/余额变化)
```

#### Py视频创作工具业务流程B-3: 代理推广流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 申请成为代理(Token)
    A->>A: 验证Token和用户资格
    A->>DB: 检查用户状态和历史记录
    alt 用户不符合代理条件
        A->>P: 返回申请失败(原因说明)
    else 用户符合条件
        A->>A: 生成唯一推广码
        A->>DB: 创建代理记录
        A->>R: 缓存代理信息
        A->>P: 返回代理申请成功(推广码)
    end

    Note over P: 查询推广数据
    P->>A: 查询推广统计(Token)
    A->>DB: 查询推广用户数量
    A->>DB: 查询推广收益统计
    A->>P: 返回推广数据(用户数/收益/转化率)

    Note over P: 新用户通过推广码注册
    P->>A: 用户注册(推广码)
    A->>DB: 验证推广码有效性
    A->>DB: 创建用户并绑定推广关系
    A->>DB: 记录推广奖励
    A->>R: 更新代理统计缓存
```

#### Py视频创作工具业务流程B-4: 代理结算流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    P->>A: 查询可结算金额(Token)
    A->>A: 验证代理身份
    A->>DB: 计算未结算推广收益
    A->>P: 返回可结算金额

    P->>A: 申请提现结算(金额/收款方式/Token)
    A->>A: 验证提现参数和最小金额
    A->>DB: 检查可结算余额
    alt 余额不足或不满足提现条件
        A->>P: 返回提现失败(原因)
    else 满足提现条件
        A->>DB: 创建提现申请记录
        A->>DB: 冻结对应金额
        A->>P: 返回提现申请成功(审核中)

        Note over A: 管理员审核通过后
        A->>TP: 调用支付接口转账
        TP->>A: 返回转账结果
        alt 转账成功
            A->>DB: 更新提现状态为成功
            A->>DB: 扣除已结算金额
            A->>R: 更新代理收益缓存
            A->>P: 推送提现成功通知
        else 转账失败
            A->>DB: 更新提现状态为失败
            A->>DB: 解冻冻结金额
            A->>P: 推送提现失败通知
        end
    end
```

#### Py视频创作工具业务流程B-5: 数据处理流程
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant FS as 文件存储

    P->>A: 上传项目数据(文件/参数/Token)
    A->>A: 验证Token和文件格式
    A->>A: 验证文件大小和类型
    alt 文件不符合要求
        A->>P: 返回上传失败(格式/大小错误)
    else 文件符合要求
        A->>FS: 存储文件到临时目录
        A->>A: 解析和验证数据内容
        A->>DB: 保存数据处理记录
        A->>R: 缓存处理状态
        A->>P: 返回上传成功(数据ID)

        Note over A: 异步处理数据
        A->>A: 数据清洗和格式化
        A->>DB: 更新处理进度
        A->>R: 更新缓存状态

        alt 数据处理成功
            A->>DB: 保存处理结果
            A->>FS: 移动文件到正式目录
            A->>P: 推送处理完成通知
        else 数据处理失败
            A->>DB: 记录错误信息
            A->>FS: 清理临时文件
            A->>P: 推送处理失败通知
        end
    end
```

### 🔄 项目管理流程

#### 🔄 Py视频创作工具 - AI核心流程

##### Py视频创作工具业务流程D-1: 视频创作项目创建流程（纯文本数据处理版）
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线
    participant CB as 角色绑定界面

    Note over A: 重要：本流程仅处理文本数据，严禁储存或中转任何资源文件

    Note over U: 用户进入项目创建页面

    U->>F: 访问视频创作页面
    F->>A: API请求

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token

    alt Token验证失败
        A->>F: 返回认证失败
        F->>U: 跳转到登录页面
    else Token验证通过
        A->>DB: 查询用户状态
        F->>A: GET /py-api/projects/styles
        A->>DB: 查询项目风格
        DB->>A: 返回风格列表
        A->>F: 返回风格数据
        F->>U: 渲染创建对话框
    end

    Note over U: 📝 故事创建模式选择
    F->>U: 显示故事模式选择界面

    alt 用户选择"自有故事"模式
        U->>F: 选择"使用我的故事"
        F->>U: 显示故事输入框
        U->>F: 输入自己的故事内容
        Note over F: 用户将在后续点击"拆解分镜"按钮
    else 用户选择"智能故事"模式
        U->>F: 选择"智能故事"
        F->>U: 显示故事要求输入框
        U->>F: 输入故事生成要求/提示
        Note over F: 系统将先生成故事再进行分镜拆解
    end

    U->>F: 输入项目信息(风格选择等)

    Note over F: 🤖 智能平台选择流程
    F->>A: POST /py-api/ai-models/select-platform<br/>business_type=story, auto_recommend=true
    A->>A: 调用智能平台选择服务
    A->>SC: 调用AiServiceClient
    SC->>DB: 查询用户历史偏好和使用记录
    SC->>SC: 分析用户偏好+平台状态+任务特性
    SC->>A: 返回最佳推荐+备选方案
    A->>F: 返回：推荐"DeepSeek(质量最佳)"<br/>+ 备选[MiniMax]

    Note over F: 🚀 用户体验优化
    alt 用户满意推荐
        F->>U: 直接点击"开始创作"<br/>使用推荐的DeepSeek
    else 用户需要更多选择
        F->>U: 点击"选择其他平台"<br/>从备选方案中选择MiniMax
    end

    Note over F: 🔗 建立WebSocket连接
    F->>W: 建立WebSocket连接
    W->>F: 连接确认，准备接收进度推送

    alt 自有故事模式处理
        Note over U: 📖 自有故事分镜拆解流程
        U->>F: 点击"拆解分镜"按钮

        F->>W: 故事分镜拆解请求
        W->>A: 转发分镜拆解请求

        A->>W: 推送进度更新(20%, "开始故事分析")
        W->>F: 实时推送进度到前端

        A->>SC: 调用故事分镜拆解服务(使用预选平台)
        SC->>AI: 对故事进行取名和分镜拆解
        AI->>SC: 返回故事标题和分镜结果

        A->>W: 推送进度更新(80%, "分镜拆解完成")
        W->>F: 实时推送进度到前端

        SC->>A: 返回分镜拆解结果
        A->>W: 返回故事标题和分镜数据
        W->>F: 推送分镜拆解成功结果
        F->>U: 显示故事标题和分镜列表(可编辑)

        Note over U: 用户可以合并、修改、删除分镜，或清除项目
        U->>F: 确认分镜内容后点击"创建项目"

    else 智能故事模式处理
        Note over U: 🤖 智能故事生成+分镜拆解流程
        U->>F: 点击"创建项目"按钮

        F->>W: 智能故事生成请求
        W->>A: 转发故事生成请求

        A->>W: 推送进度更新(10%, "开始故事生成")
        W->>F: 实时推送进度到前端

        A->>SC: 调用智能故事生成服务(使用预选平台)
        SC->>AI: 根据用户要求生成完整故事
        AI->>SC: 返回生成的故事内容

        A->>W: 推送进度更新(50%, "故事生成完成，开始分镜拆解")
        W->>F: 实时推送进度到前端

        SC->>AI: 对生成的故事进行分镜拆解
        AI->>SC: 返回分镜结果
        SC->>A: 返回故事内容和分镜数据

        A->>W: 推送进度更新(80%, "分镜拆解完成")
        W->>F: 实时推送进度到前端

        A->>W: 返回完整的故事和分镜数据
        W->>F: 推送生成成功结果
        F->>U: 显示生成的故事和分镜列表
    end

    Note over F: 📊 项目创建最终处理
    F->>W: 最终项目创建请求(包含确认的故事和分镜)
    W->>A: 转发项目创建请求

    Note over A: 📊 标准化积分处理流程
    A->>DB: 检查用户积分(事务锁定)

    alt 积分不足
        Note over A: 积分 < 所需积分
        A->>W: 返回积分不足详细信息
        W->>F: 推送积分不足消息(包含充值建议)
        F->>U: 显示积分不足提示
        Note over A: 无扣费操作，保护用户资金
    else 积分充足
        A->>DB: 扣取积分(冻结状态)
        A->>R: 同步积分状态(缓存更新)
        A->>DB: 写入业务日志(状态:冻结)
        A->>R: 缓存业务日志
        A->>RM: 创建项目资源记录

        Note over A: 📈 实时进度推送：开始最终项目创建
        A->>W: 推送进度更新(90%, "创建项目记录")
        W->>F: 实时推送进度到前端

        Note over A: 🎯 保存项目数据到数据库
        A->>DB: 创建项目记录(包含故事和分镜数据)
        DB->>A: 返回项目ID

        A->>RM: 更新资源信息

        alt 项目创建失败
            A->>W: 返回失败结果
            W->>F: 推送失败结果(详细错误信息)
            A->>DB: 更新业务日志(状态:失败)
            A->>DB: 退还积分(解冻→退还)
            A->>R: 同步退还状态
            A->>E: 发布失败事件(异步处理)
            F->>U: 显示项目创建失败提示
            Note over A: 业务失败，积分已退还
        else 项目创建成功
            A->>DB: 更新业务日志(状态:成功)
            A->>DB: 确认积分扣取(解冻→已扣)
            A->>R: 同步最终状态

            A->>W: 推送进度更新(100%, "项目创建完成")
            W->>F: 实时推送最终完成状态

            Note over A: 🎭 故事角色提取流程
            A->>W: 推送进度更新(85%, "开始提取故事角色")
            W->>F: 实时推送进度到前端

            A->>SC: 调用故事角色提取服务(使用预选平台)
            SC->>AI: 分析故事内容提取角色信息
            AI->>SC: 返回角色列表(动态数量)
            SC->>A: 返回角色提取结果

            A->>W: 推送进度更新(95%, "保存角色信息")
            W->>F: 实时推送进度到前端

            A->>DB: 保存提取的角色信息到project_characters表
            DB->>A: 角色信息保存成功

            A->>W: 返回项目创建成功结果
            W->>F: 推送项目创建成功结果

            Note over A: 📊 用户偏好学习与优化
            A->>DB: 记录用户平台选择行为<br/>(智能推荐/选择理由/故事生成任务/生成质量)
            A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/成本优先)
            A->>R: 更新用户常用平台缓存
            A->>R: 刷新推荐算法缓存<br/>为下次推荐优化准备数据
        end
    end

    Note over F: 🔚 关闭WebSocket连接
    F->>W: 关闭WebSocket连接

    Note over F: 🎯 跳转到下一个业务流程
    F->>F: 跳转到角色绑定页面
    F->>CB: 传递项目创建结果<br/>{<br/>  code: 200,<br/>  message: "项目创建成功",<br/>  data: {<br/>    project_id, next_step: "character_binding"<br/>  },<br/>  timestamp, request_id<br/>}

    Note over CB: 项目创建流程完成，角色绑定界面接收项目数据并开始角色绑定流程
```

**🎯 图表中体现的标准化流程特性：**

- **📖 双模式故事创建：** 支持"自有故事"（用户输入故事内容进行分镜拆解）和"智能故事"（AI生成完整故事）两种创建模式，满足不同用户需求
- **🤖 智能平台选择：** 调用智能平台选择服务，基于用户偏好和平台状态提供最佳推荐和备选方案
- **🎬 故事分镜拆解流程：** 支持对故事进行智能分镜拆解，生成可编辑的分镜列表，用户可合并、修改、删除分镜
- **🔧 统一平台架构：** 所有平台选择通过AiServiceClient统一管理，支持deepseek/minimax等多平台
- **🔒 标准化积分处理（引用C-2、C-3、C-4）：** 事务锁定检查用户积分、积分冻结状态扣取、失败时事务保证返还积分
- **⚠️ 错误处理机制（引用C-4）：** 事件总线异步处理，发布失败事件进行标准化失败处理
- **📦 资源管理标准化（引用C-6）：** AI资源管理服务创建和更新项目资源记录
- **📝 纯文本数据处理：** 本流程仅处理故事文本和分镜数据，严禁储存或中转任何资源文件
- **🔄 环境切换统一（引用C-9）：** 所有AI调用通过AiServiceClient，支持环境切换机制
- **📊 实时进度推送：** 通过WebSocket服务实时推送故事生成、分镜拆解、项目创建等各阶段进度
- **🎯 用户偏好学习：** 记录用户平台选择行为和偏好权重，为后续智能推荐优化准备数据
- **🔐 标准化Token验证：** 复用diagram-22-python-token-validation.html标准Token验证流程
- **💾 数据库操作：** 创建项目记录，包含故事和分镜数据，缓存项目信息到Redis

**📚 C系列图表引用说明：**

图表中的引用标注说明：
- **C-1：** AI任务调度 - 智能平台选择机制
- **C-2：** AI生成成功 - 积分确认扣取流程
- **C-3：** 积分不足 - 快速验证机制
- **C-4：** AI生成失败 - 事件总线异步处理
- **C-6：** 资源管理 - AI资源管理服务
- **C-7：** 资源下载 - 直接下载机制
- **C-8：** 作品发布 - 可选发布流程
- **C-9：** 环境切换 - AiServiceClient统一调用

*这些引用标注帮助开发者理解当前流程与标准化规范的对应关系，确保实现的一致性。*

##### Py视频创作工具业务流程D-2: AI分镜剧本生成与场景分镜数据入库流程（增强版）

**图表文件**: `chart/diagram-Storyboard-ai-shared.html`

**流程概述**:
- 增强了场景信息解析和分镜数据验证功能
- 新增project_scenarios表支持场景管理
- 修改project_storyboards表结构以支持场景关联
- 增强ProjectStoryboardService方法以支持场景和分镜的批量创建

**主要增强点**:
1. **解析场景和分镜信息**: 增强parseAiStoryboardJson()方法支持场景信息提取
2. **验证分镜数据完整性**: 新增validateStoryboardData()方法验证必需字段
3. **批量创建场景和分镜**: 新增batchCreateScenarios()和batchCreateStoryboards()方法

**数据库变更**:
- 新增project_scenarios表：存储场景名称、空间、时间、天气等信息
- 修改project_storyboards表：删除6个字段、scene_description改为subtitle、增加scenarios_id外键

**技术实现**:
- 使用增强的ProjectStoryboardService.extractStoryboardsFromStory()方法
- 支持场景与分镜的关联关系管理
- 保持与现有AI生成流程的兼容性

##### Py视频创作工具业务流程D-3: 绑定角色流程（标准化版）
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线
    participant PG as 剧情图片生成界面

    Note over U: 用户从项目创建页面跳转到角色绑定页面

    Note over F: 🔐 Token验证流程
    Note over F: 引用 diagram-22-python-token-validation.html
    F->>A: 提交角色绑定页面请求(包含Token和project_id)
    A->>A: 验证Token和项目参数
    A->>DB: 查询用户状态和项目权限

    Note over F: 🎭 角色绑定页面初始化流程
    F->>A: GET /py-api/projects/{id}/characters
    A->>DB: 查询项目的角色信息(project_characters表)
    A->>R: 检查角色缓存
    A->>F: 返回项目角色列表(动态数量)

    F->>F: 根据角色数量动态生成坑位界面
    F->>U: 显示角色绑定界面(动态N个角色坑位)

    loop 每个提取的角色
        F->>U: 显示角色坑位(角色名称+描述+选择按钮)
    end

    Note over U: 用户可以选择"一键选形象"或单独为角色选择形象
    alt 用户选择"一键选形象"
        U->>F: 点击"一键选形象"按钮
        Note over F: 为所有角色批量智能匹配形象
    else 用户单独选择角色形象
        U->>F: 点击某个角色坑位
        F->>U: 弹出角色选择对话框
    end

    Note over F: 📋 角色库查询流程
    F->>A: GET /py-api/characters/library
    A->>DB: 查询可用角色库
    A->>R: 检查角色库缓存
    A->>F: 返回角色列表
    F->>U: 显示角色选择界面(小框点击弹出角色列表)

    alt 用户选择现有角色
        U->>F: 选择现有角色进行绑定
        F->>A: 提交角色绑定请求(角色ID+分镜位置ID)
    else 用户选择新增角色
        U->>F: 点击"新增角色"按钮
        F->>F: 调用角色创建罩层<br/>PyVideoTool.CharacterCreationModal.open({<br/>mode: 'filtered', filters: {projectType, style},<br/>callbacks: {onSuccess: bindNewCharacter}})
        Note over F: 引用 diagram-character-creation-shared.html
        Note over F: 角色创建罩层处理完整创建流程
        F->>F: 接收角色创建成功回调
        F->>A: 提交新角色绑定请求(新角色ID+分镜位置ID)
    end

    Note over A: 🎯 执行角色绑定操作
    Note over A: 角色已创建完成，无需积分消费和平台选择
    A->>A: POST /py-api/characters/bind<br/>character_id={new_character_id}, storyboard_position_id={position_id}
    A->>DB: 创建角色分镜绑定记录
    A->>R: 更新绑定缓存
    DB->>A: 返回绑定配置ID和扩展信息
    A->>RM: 更新分镜资源信息

    alt 角色绑定失败
        A->>DB: 更新业务日志(状态:失败)
        A->>E: 发布失败事件(异步处理)
        A->>F: 返回失败结果
        F->>U: 显示角色绑定失败提示
        Note over A: 绑定失败，无积分操作
    else 角色绑定成功
        A->>DB: 更新业务日志(状态:成功)
        A->>F: 返回绑定配置数据
        F->>U: 显示绑定成功的角色(更新分镜中的角色显示)
        Note over A: 绑定成功，无积分操作

        Note over A: 📊 用户偏好学习与优化
        A->>DB: 记录用户平台选择行为<br/>(智能推荐/选择理由/角色绑定任务/绑定质量)
        A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/兼容性优先)
        A->>R: 更新用户常用平台缓存
        A->>R: 刷新推荐算法缓存<br/>为下次绑定优化准备数据
    end

    Note over F: 🎯 后续操作选择
    alt 用户继续编辑分镜
        F->>U: 显示更新后的分镜编辑界面
        Note over U: 用户可以继续绑定其他角色或编辑分镜
    else 用户进入生图模式
        U->>F: 点击"进入生图"按钮
        F->>F: 跳转到生图页面
        F->>PG: 传递角色绑定数据，启动剧情图片生成流程
    end

    Note over U: 角色绑定流程完成，可继续分镜编辑或进入生图模式
```

**🎯 图表中体现的标准化流程特性：**

- **🎬 分镜角色显示：** 显示分镜中的角色列表，支持点击角色位置进行绑定操作
- **📊 项目角色信息查询：** 从project_characters表查询项目的角色信息，支持动态数量的角色显示
- **⚡ 一键选形象功能：** 支持为所有角色批量智能匹配形象，提升用户操作效率
- **📋 角色选择对话框：** 点击分镜位置弹出角色选择界面，支持选择现有角色或新增角色
- **🆕 新增角色集成：** 支持在绑定流程中引用角色创建共享流程，创建完成后自动返回绑定
- **🔒 无积分消费设计：** 选择现有角色绑定不消费积分；新增角色的积分消费在角色创建流程中完成
- **⚠️ 错误处理机制：** 事件总线异步处理，发布失败事件进行标准化失败处理
- **📦 资源管理标准化：** AI资源管理服务创建和更新分镜资源记录
- **⚡ 同步操作处理：** 角色绑定为同步数据库操作，快速响应用户操作
- **🎯 用户偏好学习：** 记录用户平台选择行为和偏好权重，为后续智能推荐优化准备数据
- **🔐 标准化Token验证：** 复用diagram-22-python-token-validation.html标准Token验证流程
- **💾 数据库操作：** 创建角色分镜绑定记录，缓存绑定配置到Redis
- **🎮 后续操作支持：** 支持继续编辑分镜或进入生图模式，提供灵活的工作流程
- **🎮 后续操作支持：** 支持继续编辑分镜或进入生图模式，提供灵活的工作流程
- **⚡ 缓存优化：** 角色库和分镜信息缓存，提升查询性能

**📚 C系列图表参考说明：**

图表中的参考标注说明：
- **C-1：** AI任务调度 - 智能平台选择机制
- **C-2：** AI生成成功 - 积分确认扣取流程
- **C-3：** 积分不足 - 快速验证机制
- **C-4：** AI生成失败 - 事件总线异步处理
- **C-6：** 资源管理 - AI资源管理服务
- **C-9：** 环境切换 - AiServiceClient统一调用

*这些参考标注帮助开发者理解当前流程与标准化规范的对应关系，确保实现的一致性。*

##### 共享业务流程: 角色创建流程（可复用标准流程）

##### 共享业务流程: AI生成提示词（可复用标准流程）
- 图表文件：`chart/diagram-prompt-edit-shared.html`
- 复用组件：Token验证、智能平台选择（AiServiceClient）、标准化积分流程（C-2/C-3/C-4）、WebSocket服务、事件总线
- 应用场景：分镜编辑（回填 storyboard.ai_prompt / scene_description）、角色/场景描述、旁白脚本等
- 行为边界：仅处理文本与元数据，不中转/存储资源文件
- 可选动作：自动保存 PUT /py-api/storyboards/{id}，仅更新 ai_prompt 纯文本字段

```mermaid
sequenceDiagram
    participant Caller as 调用方业务流程
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    Note over Caller: 🔄 业务流程需要角色创建功能

    Caller->>F: 调用角色创建流程<br/>参数: {mode, filters, callback}

    Note over F: 📋 参数化配置处理
    alt mode=full（全角色列表）
        Note over F: 显示所有可用角色，无筛选条件
    else mode=filtered（条件筛选）
        Note over F: 根据filters参数筛选角色<br/>如：projectType, style, permissions等
    else mode=quick（快速创建）
        Note over F: 使用预设配置，简化创建流程
    else mode=advanced（高级创建）
        Note over F: 提供完整的角色创建功能
    end

    Note over F: 🎨 统一UI处理
    F->>F: 弹出角色创建罩层界面
    F->>A: 获取角色创建初始数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限

    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
        F->>Caller: 返回失败结果<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
    else Token验证通过
        A->>DB: 查询用户状态和权限
        F->>A: GET /py-api/characters/styles
        A->>DB: 查询角色风格列表
        DB->>A: 返回风格数据
        A->>F: 返回角色创建配置数据
        F->>F: 渲染角色创建界面

        Note over F: 🎭 角色创建模式选择
        F->>F: 根据参数显示对应的创建选项

        alt 用户选择自有角色模式
            F->>F: 显示文件上传界面
            Note over F: 用户上传角色图片，验证格式和大小
        else 用户选择智能角色模式
            F->>F: 显示角色描述输入界面
            Note over F: 用户输入角色描述和提示词
        end

        F->>F: 用户选择角色风格(根据mode参数决定可选范围)

        Note over F: 🤖 智能平台选择流程
        F->>A: POST /py-api/ai-models/select-platform<br/>business_type=character_generation, auto_recommend=true
        A->>A: 调用智能平台选择服务
        A->>SC: 调用AiServiceClient
        SC->>DB: 查询用户历史偏好和使用记录
        SC->>SC: 分析用户偏好+平台状态+任务特性
        SC->>A: 返回最佳推荐+备选方案
        A->>F: 返回平台推荐结果
        F->>F: 显示平台选择界面

        F->>F: 用户确认创建参数，点击生成按钮

        Note over F: 🔗 建立WebSocket连接
        F->>W: 建立WebSocket连接
        W->>F: 连接确认，准备接收进度推送

        F->>W: 角色创建请求
        W->>A: 转发角色创建请求

        Note over A: 📊 标准化积分处理流程
        A->>DB: 检查用户积分(事务锁定)

        alt 积分不足
            Note over A: 积分 < 所需积分
            A->>W: 返回积分不足详细信息
            W->>F: 推送积分不足消息
            F->>F: 显示积分不足提示
            F->>Caller: 返回失败结果{code: 1006, message: "积分不足", data: null, timestamp, request_id}
            Note over A: 无扣费操作，保护用户资金
        else 积分充足
            A->>DB: 扣取积分(冻结状态)
            A->>R: 同步积分状态(缓存更新)
            A->>DB: 写入业务日志(状态:冻结)
            A->>R: 缓存业务日志
            A->>RM: 创建角色资源记录

            Note over A: 📈 实时进度推送
            A->>W: 推送进度更新(10%, "开始角色创建")
            W->>F: 实时推送进度到前端
            F->>F: 更新进度条显示

            Note over A: 🎯 执行角色创建处理
            alt 自有角色模式
                A->>W: 推送进度更新(30%, "处理上传的角色图片")
                W->>F: 实时推送进度
                A->>SC: 调用图片处理服务
                SC->>SC: 处理用户上传的角色图片
                A->>W: 推送进度更新(60%, "应用角色风格")
                W->>F: 实时推送进度
            else 智能角色模式
                A->>W: 推送进度更新(30%, "连接AI平台")
                W->>F: 实时推送进度
                A->>SC: 调用AI生成服务
                A->>W: 推送进度更新(60%, "AI处理中")
                W->>F: 实时推送进度
                SC->>AI: 生成角色图像
                AI->>SC: 返回生成结果
            end

            A->>W: 推送进度更新(80%, "保存角色数据")
            W->>F: 实时推送进度

            alt 角色创建失败
                A->>W: 返回失败结果
                W->>F: 推送失败结果
                A->>DB: 更新业务日志(状态:失败)
                A->>DB: 退还积分(解冻→退还)
                A->>R: 同步退还状态
                A->>E: 发布失败事件(异步处理)
                F->>F: 显示创建失败提示
                F->>Caller: 返回失败结果{code: 5002, message: "角色生成失败", data: null, timestamp, request_id}
                Note over A: 业务失败，积分已退还
            else 角色创建成功
                A->>DB: 更新业务日志(状态:成功)
                A->>DB: 确认积分扣取(解冻→已扣)
                A->>R: 同步最终状态
                A->>RM: 更新资源信息

                A->>W: 推送进度更新(100%, "角色创建完成")
                W->>F: 实时推送最终完成状态
                F->>F: 显示创建成功界面

                A->>W: 返回角色数据
                W->>F: 推送角色创建成功结果
                F->>F: 关闭角色创建罩层
                F->>Caller: 返回成功结果<br/>{<br/>  code: 200,<br/>  message: "角色生成任务创建成功",<br/>  data: {<br/>    task_id, status,<br/>    estimated_cost, platform<br/>  },<br/>  timestamp, request_id<br/>}

                Note over A: 📊 用户偏好学习与优化
                A->>DB: 记录用户平台选择行为和偏好权重
                A->>R: 更新用户常用平台缓存
                A->>R: 刷新推荐算法缓存
            end
        end

        Note over F: 🔚 清理资源
        F->>W: 关闭WebSocket连接
    end

    Note over Caller: 🎯 接收角色创建结果，继续后续业务流程
```

**🎯 可复用流程特性：**

- **🔄 标准化调用接口：** 统一的参数化调用方式，支持不同业务场景的需求
- **🔐 标准化Token验证：** 复用diagram-22-python-token-validation.html标准Token验证流程
- **🎨 UI统一处理：** 所有角色创建的UI交互都由"Py视频创作工具前端"统一处理，确保用户体验一致性
- **📋 多模式支持：** 支持全角色列表、条件筛选、快速创建、高级创建等多种调用模式
- **🎭 双创建模式：** 支持自有角色上传和智能角色生成两种创建方式
- **🤖 智能平台选择：** 集成标准化的AI平台选择机制，提供最佳推荐
- **🔒 完整积分处理：** 包含积分验证、冻结、扣除、返还的完整流程
- **📡 实时进度推送：** 通过WebSocket提供实时的创建进度反馈
- **⚠️ 错误处理机制：** 完善的错误处理和用户提示机制
- **🔄 状态管理：** 完整的业务状态跟踪和数据同步
- **📊 用户偏好学习：** 记录用户行为，优化后续推荐
- **🎯 结果回调：** 标准化的成功/失败结果返回机制
- **🔧 可扩展配置：** 支持灵活的参数配置和功能扩展

**📚 引用说明：**

本流程可被以下业务流程引用：
- **D-1：** 项目创建流程（项目创建时需要角色）
- **D-2：** 角色绑定流程（绑定时需要新建角色）
- **其他：** 任何需要角色创建功能的业务流程

**📋 调用方式：**
```javascript
// 标准调用接口
PyVideoTool.CharacterCreationModal.open({
    mode: 'full|filtered|quick|advanced',
    filters: { projectType, styles, permissions },
    callbacks: { onSuccess, onCancel, onError, onProgress }
});
```

**📚 技术规范参考：**

图表中的参考标注说明：
- **C-1：** AI任务调度 - 智能平台选择机制
- **C-2：** AI生成成功 - 积分确认扣取流程
- **C-3：** 积分不足 - 快速验证机制
- **C-4：** AI生成失败 - 事件总线异步处理
- **C-6：** 资源管理 - AI资源管理服务
- **C-7：** 资源下载 - 直接下载机制
- **C-8：** 作品发布 - 可选发布流程
- **C-9：** 环境切换 - AiServiceClient统一调用

*这些规范确保了与系统其他流程的一致性和兼容性。*

##### 共享业务流程: 音色试听流程（可复用标准流程）
```mermaid
sequenceDiagram
    participant Caller as 调用方业务流程
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant API as 工具API接口服务
    participant Auth as PyApi认证服务
    participant SNS as 分镜旁白服务
    participant VS as 语音服务
    participant AiClient as AI服务客户端
    participant AiAPI as AI服务模拟/真实平台

    Note over Caller,AiAPI: 🎵 音色试听流程 - 支持分镜旁白配置与试听预览

    %% 1. 获取项目角色列表（字幕配音下拉）
    Caller->>F: 触发获取项目角色列表
    F->>API: GET /py-api/projects/{project_id}/characters
    API->>Auth: 验证Token（参考diagram-22-python-token-validation.html）
    Auth-->>API: 认证成功
    API->>SNS: 获取项目绑定角色列表
    SNS-->>API: 返回角色列表
    API-->>F: 200 - 角色列表数据
    F-->>Caller: 展示字幕配音下拉选项

    %% 2. 设置分镜解说角色
    Caller->>F: 选择解说角色
    F->>API: PUT /py-api/storyboards/{id}/narration
    Note right of F: 参数：narrator_character_id, subtitle_text(可选)
    API->>Auth: 验证Token
    Auth-->>API: 认证成功
    API->>SNS: 设置分镜解说角色
    SNS->>SNS: 权限校验+角色归属验证
    SNS->>SNS: 创建/更新旁白记录
    SNS-->>API: 返回旁白配置详情
    API-->>F: 200 - 解说角色设置成功
    F-->>Caller: 更新UI状态

    %% 3. 获取火山音色列表（更改角色音色下拉）
    Caller->>F: 触发获取音色列表
    F->>API: GET /py-api/voices/providers/volcengine/list?type=traditional
    API->>Auth: 验证Token
    Auth-->>API: 认证成功
    API->>VS: 获取火山音色列表
    VS->>AiClient: 调用AI服务客户端
    AiClient->>AiAPI: 请求音色列表（开发阶段走aiapi，生产走真实平台）
    AiAPI-->>AiClient: 返回音色数据
    AiClient-->>VS: 音色列表
    VS-->>API: 返回音色列表
    API-->>F: 200 - 音色列表数据
    F-->>Caller: 展示音色下拉选项

    %% 4. 设置分镜旁白音色
    Caller->>F: 选择音色
    F->>API: PUT /py-api/storyboards/{id}/narration/voice
    Note right of F: 参数：voice_id, platform, language, emotion, speed, pitch(可选)
    API->>Auth: 验证Token
    Auth-->>API: 认证成功
    API->>SNS: 设置旁白音色
    SNS->>SNS: 参数校验（语言/情绪/速度/音调范围）
    SNS->>SNS: 更新旁白音色配置
    SNS-->>API: 返回旁白配置详情
    API-->>F: 200 - 音色设置成功
    F-->>Caller: 更新UI状态

    %% 5. 试听对白
    Caller->>F: 点击试听按钮
    F->>API: POST /py-api/voice/preview
    Note right of F: 参数：storyboard_id, text(可选，默认使用分镜旁白文本)
    API->>Auth: 验证Token
    Auth-->>API: 认证成功
    API->>SNS: 生成试听音频
    SNS->>SNS: 聚合分镜旁白配置（角色+文本+音色）
    SNS->>AiClient: 调用AI语音合成
    AiClient->>AiAPI: 请求音频生成（开发阶段走aiapi，生产走真实平台）
    AiAPI-->>AiClient: 返回音频URL
    AiClient-->>SNS: 音频URL
    SNS->>SNS: 写入last_preview_url（便于前端复用）
    SNS-->>API: 返回试听结果
    API-->>F: 200 - 试听音频URL+配置详情
    F-->>Caller: 播放试听音频

    %% 错误处理
    alt 认证失败
        Auth-->>API: 401 - Token无效
        API-->>F: 401 - 认证失败
        F-->>Caller: 提示重新登录
    else 权限不足
        SNS-->>API: 403 - 无权访问分镜
        API-->>F: 403 - 权限不足
        F-->>Caller: 提示权限错误
    else 参数错误
        SNS-->>API: 422 - 参数验证失败
        API-->>F: 422 - 参数错误
        F-->>Caller: 提示参数错误
    else AI服务异常
        AiAPI-->>AiClient: 500 - 服务异常
        AiClient-->>SNS: 音频生成失败
        SNS-->>API: 500 - 试听生成失败
        API-->>F: 500 - 服务异常
        F-->>Caller: 提示稍后重试
    end

    Note over Caller,AiAPI: 🚨 架构边界规范遵循
    Note over API,AiAPI: ✅ 仅返回URL与元数据，禁止中转音频资源
    Note over AiClient,AiAPI: ✅ 开发阶段走aiapi模拟，生产通过ai.php切换真实平台
    Note over Auth: ✅ 统一使用PyApi认证服务，遵循Token验证标准
    Note over SNS: ✅ 独立表设计（storyboard_narrations），支持历史版本与扩展
```

**流程关键点说明：**
- **认证统一**: 所有接口统一使用PyApi\AuthService认证，遵循diagram-22-python-token-validation.html标准
- **架构边界**: 严格遵循资源下载架构边界，仅返回URL与元数据，禁止服务器中转音频资源
- **环境切换**: 开发阶段通过AiServiceClient调用aiapi模拟，生产环境通过php/api/config/ai.php切换真实平台
- **数据设计**: 采用独立表storyboard_narrations（倾向B），支持历史版本与功能扩展
- **参数支持**: language/emotion/speed/pitch作为可选参数，前端按需传入，后端提供默认值与范围校验
- **复用机制**: last_preview_url写入便于前端复用，避免重复生成相同配置的试听音频

*此流程可被其他需要音色试听功能的业务场景复用，确保系统一致性。*

##### 共享业务流程: 修改绑定角色流程（可复用标准流程）
```mermaid
sequenceDiagram
    participant Caller as 调用方业务流程
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    Note over Caller: 业务流程需要修改角色绑定功能

    Caller->>F: 调用修改绑定角色流程

    Note over F: 参数化配置处理
    F->>F: 弹出修改绑定角色界面
    F->>A: 获取当前绑定信息

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限

    alt Token验证失败
        A->>F: 返回认证失败
        F->>F: 显示登录提示
        F->>Caller: 返回失败结果
    else Token验证通过
        A->>DB: 查询用户状态和权限
        F->>A: GET /py-api/characters/bindings/{id}
        A->>DB: 查询当前绑定信息
        A->>F: 返回当前绑定配置数据
        F->>F: 渲染修改绑定界面

        Note over F: 显示当前绑定信息
        F->>F: 用户确认解绑当前角色
        F->>A: DELETE /py-api/characters/unbind
        A->>DB: 解除当前角色绑定关系
        A->>F: 返回解绑成功确认

        Note over F: 选择新角色流程
        alt 用户选择现有角色
            F->>A: GET /py-api/characters/list
            A->>F: 返回角色列表
            F->>F: 显示角色选择界面
        else 用户选择创建新角色
            F->>F: 调用角色创建罩层<br/>CharacterBindingModification.CharacterCreationModal.open({<br/>mode: 'filtered', filters: {projectType, style},<br/>callbacks: {onSuccess: bindNewCharacter}})
            Note over F: 引用 diagram-character-creation-shared.html
            Note over F: 角色创建罩层处理完整创建流程
            F->>F: 接收角色创建成功回调
        end

        F->>F: 用户确认修改参数
        F->>A: 修改绑定角色请求

        Note over A: 执行角色绑定修改
        Note over A: 无需积分消费，角色已存在或已创建完成
        A->>A: POST /py-api/characters/bind
        A->>DB: 创建新的角色绑定关系

        alt 绑定修改失败
            A->>F: 返回失败结果
            F->>Caller: 返回失败结果
        else 绑定修改成功
            A->>F: 返回修改后的绑定数据
            F->>F: 显示修改成功界面
            F->>Caller: 返回成功结果
        end
    end
```

**🎯 可复用流程特性：**

- **🔄 标准化调用接口：** 统一的参数化调用方式，支持不同业务场景的绑定修改需求
- **🔐 标准化Token验证：** 复用diagram-22-python-token-validation.html标准Token验证流程
- **🎨 UI统一处理：** 所有绑定修改的UI交互都由"Py视频创作工具前端"统一处理，确保用户体验一致性
- **📋 多模式支持：** 支持直接替换、确认后替换、批量修改、高级修改等多种调用模式
- **🔓 安全解绑流程：** 先安全解除当前绑定关系，确保数据一致性
- **🎭 灵活角色选择：** 支持选择现有角色或创建新角色进行绑定
- **🔄 复用角色创建流程：** 创建新角色时复用diagram-character-creation-shared.html标准流程，确保一致性
- **🔒 无积分消费设计：** 修改绑定关系本身不消费积分，角色创建的积分消费在创建流程中完成
- **⚡ 同步操作处理：** 修改绑定关系为同步数据库操作，快速响应用户操作
- **⚠️ 错误处理机制：** 完善的错误处理和用户提示机制
- **🔄 状态管理：** 完整的业务状态跟踪和数据同步
- **📊 用户偏好学习：** 记录用户行为，优化后续推荐
- **🎯 结果回调：** 标准化的成功/失败结果返回机制
- **🔧 可扩展配置：** 支持灵活的参数配置和功能扩展

**📚 引用说明：**

本流程可被以下业务流程引用：
- **分镜编辑流程：** 在分镜编辑过程中修改角色绑定
- **角色管理流程：** 批量修改角色绑定关系
- **项目优化流程：** 优化项目中的角色配置
- **其他：** 任何需要修改角色绑定功能的业务流程

Py视频创作工具 - AI处理流程规范

#### 🔄 Py视频创作工具 - AI处理流程规范

##### Py视频创作工具业务流程C-1: AI任务调度流程（用户选择平台+环境切换版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant Mock as 虚拟AI平台
    participant DeepSeek as DeepSeek API
    participant LiblibAI as LiblibAI API
    participant KlingAI as KlingAI API
    participant MiniMax as MiniMax API
    participant Doubao as 火山引擎豆包 API
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over P: 🎬 视频创作工具启动（优化版）

    %% 一键智能推荐（合并接口125+132）
    P->>A: POST /py-api/ai-models/select-platform<br/>business_type=video, auto_recommend=true
    A->>A: 调用智能平台选择服务
    A->>SC: 调用AiServiceClient
    SC->>DB: 查询用户历史偏好和使用记录
    SC->>SC: 分析用户偏好+平台状态+任务特性
    SC->>A: 返回最佳推荐+备选方案
    A->>P: 返回：推荐"KlingAI(质量最佳)"<br/>+ 备选[LiblibAI, MiniMax]

    Note over P: 🚀 用户体验优化
    alt 用户满意推荐
        P->>P: 直接点击"开始创作"<br/>使用推荐的KlingAI
    else 用户需要更多选择
        P->>P: 点击"选择其他平台"<br/>从备选方案中选择LiblibAI
    end

    Note over P: 📝 提交视频生成任务

    Note over P: 🔗 建立WebSocket连接
    P->>W: 建立WebSocket连接
    W->>P: 连接确认，准备接收进度推送

    P->>W: 提交AI任务(video_generation/参数/选择的平台/Token)
    W->>A: 转发任务请求
    A->>A: 验证Token和任务参数
    A->>DB: 检查用户积分和权限
    A->>DB: 创建AI任务记录(含用户选择的平台+选择方式)

    Note over A: 📈 实时进度推送：开始AI任务调度
    A->>W: 推送进度更新(10%, "开始AI任务调度")
    W->>P: 实时推送进度到前端

    A->>A: 调用智能平台选择服务(指定平台)
    A->>SC: 调用AiServiceClient(指定平台)

    A->>W: 推送进度更新(30%, "连接AI平台")
    W->>P: 实时推送进度到前端

    Note over SC: 🚨 环境切换机制
    alt 开发环境 (AI_SERVICE_MODE=mock)
        SC->>Mock: 调用虚拟AI平台<br/>模拟用户选择的平台响应
        Mock->>SC: 返回模拟结果(快速响应)

    else 生产环境 (AI_SERVICE_MODE=real)
        alt 用户选择DeepSeek
            SC->>DeepSeek: 调用DeepSeek API
            DeepSeek->>SC: 返回生成结果
        else 用户选择LiblibAI
            SC->>LiblibAI: 调用LiblibAI API
            LiblibAI->>SC: 返回生成结果
        else 用户选择KlingAI
            SC->>KlingAI: 调用KlingAI API
            KlingAI->>SC: 返回生成结果
        else 用户选择MiniMax
            SC->>MiniMax: 调用MiniMax API
            MiniMax->>SC: 返回生成结果
        else 用户选择火山引擎豆包
            SC->>Doubao: 调用火山引擎豆包 API
            Doubao->>SC: 返回生成结果
        end
    end

    A->>W: 推送进度更新(80%, "AI处理完成")
    W->>P: 实时推送进度到前端

    SC->>A: 返回AI生成结果(含实际使用的平台信息)
    A->>DB: 更新任务状态、结果和平台使用记录
    A->>R: 缓存任务结果

    A->>W: 推送进度更新(100%, "任务调度完成")
    W->>P: 实时推送最终完成状态

    A->>W: 返回AI生成结果
    W->>P: 推送最终结果(含实际使用的平台信息)

    Note over P: 🔚 关闭WebSocket连接
    P->>W: 关闭WebSocket连接

    Note over A: 📊 用户偏好学习与优化
    A->>DB: 记录用户平台选择行为<br/>(手动选择/智能推荐/选择理由)
    A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/成本优先)
    A->>R: 更新用户常用平台缓存
    A->>R: 刷新推荐算法缓存<br/>为下次推荐优化准备数据
```

**🔧 平台选择架构参考规范**

⚠️ **重要提醒：其他业务流程参考本图表的平台选择机制时**

- **✅ 关注具体AI平台参与者：** Py视频创作工具->WebSocket服务->工具API接口服务->AiServiceClient->MySQL数据库
- **🎯 完整流程链路：** 从用户操作到数据存储的完整业务流程，每个参与者都有明确的职责分工
- **🔧 智能平台选择职责：** 负责智能平台选择逻辑，调用AiServiceClient获取平台信息并返回最佳推荐
- **📊 AiServiceClient职责：** 负责与具体AI平台的交互，封装平台差异，提供统一的服务接口
- **🔄 环境切换机制：** 虚拟AI平台(Mock)、生产环境平台切换等技术实现细节在AiServiceClient内部处理

*遵循此规范可确保业务流程图表的简洁性和可维护性，避免不必要的技术实现细节干扰业务逻辑表达。*

**📋 AI任务调度核心特性**

- **🤖 智能平台推荐：** 基于用户历史偏好、平台状态和任务特性进行智能分析推荐
- **🔄 环境切换机制：** 支持虚拟AI平台(Mock)和生产环境的无缝切换
- **📊 实时状态监控：** 通过WebSocket服务实时推送任务调度和执行状态
- **⚡ 统一调度接口：** 所有AI任务通过统一的调度机制进行管理和执行

#### Py视频创作工具业务流程C-2: AI生成成功流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分(事务锁定)
    A->>DB: 扣取积分(冻结状态)
    A->>R: 同步积分状态(缓存更新)
    A->>DB: 写入业务日志(状态:冻结)
    A->>R: 缓存业务日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 发送AI生成请求
    AI->>SC: 返回AI生成结果
    SC->>A: 返回处理结果
    A->>DB: 确认积分扣取(解冻)
    A->>R: 更新积分缓存
    A->>DB: 更新业务日志(状态:成功)
    A->>R: 更新业务缓存
    A->>E: 发布成功事件(异步)
    A->>W: 返回成功结果
    W->>P: 推送成功通知
```

#### Py视频创作工具业务流程C-3: 积分不足业务流程（优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>A: 检查用户积分(快速验证)
    Note over A: 积分 < 所需积分
    A->>W: 返回积分不足详细信息
    W->>P: 推送积分不足消息(包含充值建议)
    Note over A: 无扣费操作，保护用户资金
```

#### Py视频创作工具业务流程C-4: 处理失败的业务流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)
    AI->>SC: 返回失败结果
    SC->>A: 返回失败结果+环境模式信息
    A->>W: 返回失败结果
    W->>P: 推送失败结果(详细错误信息)
    W->>E: 发布任务失败事件(异步)
    E->>A: 处理任务失败事件
    A->>DB: 更新日志状态(失败) + 返还等额积分(事务保证)
    A->>R: 更新缓存状态
    Note over SC: 环境切换完成，积分安全返还
```

#### Py视频创作工具业务流程C-5: 超时/中断处理业务流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant T as 超时监控
    participant E as 事件总线

    P->>W: 发起AI生成请求
    W->>A: 提交业务信息+请求密钥
    A->>DB: 检查用户积分 → 扣取积分(冻结状态)
    A->>R: 同步积分状态 → 写入业务日志 → 缓存日志
    A->>SC: 调用AiServiceClient
    W->>T: 启动超时监控(业务类型自适应)
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务(自动环境切换)

    alt 超时或连接中断
        T->>E: 检测到超时/中断(发布事件)
        E->>A: 处理中断事件
        A->>DB: 更新日志状态(失败) + 返还等额积分
        A->>R: 更新缓存状态
        E->>W: 通知WebSocket服务
        W->>P: 推送中断消息(包含积分返还确认)
        Note over SC: 环境切换机制保证服务稳定性
    end
```



#### Py视频创作工具业务流程C-6: AI资源生成与版本管理流程（环境切换优化版）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant RM as AI资源管理服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    P->>A: 发起AI资源生成请求(含module_id)
    A->>RM: 创建资源记录
    RM->>DB: 创建p_resources记录
    RM->>DB: 自动创建v1.0版本记录
    RM->>A: 返回资源UUID
    A->>SC: 调用AiServiceClient
    Note over SC: 🚨环境切换：根据AI_SERVICE_MODE<br/>自动路由到mock/real服务
    SC->>AI: 调用AI服务生成资源
    AI->>SC: 返回资源URL和元数据
    SC->>A: 返回结果+环境模式信息
    A->>RM: 更新版本信息
    RM->>DB: 更新resource_url、file_size等
    RM->>RM: 执行自动内容审核
    RM->>DB: 更新review_status
    RM->>R: 缓存资源状态
    A->>P: 返回资源信息
    P->>AI: 直接下载资源到本地
    P->>A: 确认下载完成
    A->>RM: 更新下载状态
    RM->>DB: 更新downloaded_by_python=true
```

#### Py视频创作工具业务流程C-7: 资源下载完成流程（核心流程）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant AI as AI平台/模拟服务
    participant DB as MySQL数据库

    P->>A: 请求资源下载信息(resource_id)
    A->>DB: 查询资源信息和AI平台URL
    DB->>A: 返回resource_url和元数据
    A->>P: 返回AI平台URL和文件信息
    Note over SC: 🚨环境切换：资源URL根据环境<br/>指向模拟服务或真实AI平台
    P->>AI: 直接从AI平台下载资源
    AI->>P: 下载完成
    P->>A: 确认下载完成(local_path)
    A->>DB: 更新下载状态和本地路径
    Note over P: 创作完成，资源已保存到本地
```

#### Py视频创作工具业务流程C-8: 可选作品发布流程（增值服务）
```mermaid
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant WPS as 作品发布权限服务
    participant WP as 作品广场
    participant DB as MySQL数据库

    Note over P: 用户已完成本地导出
    P->>A: [可选] 请求发布作品(module_type, module_id)
    A->>WPS: 检查发布权限
    WPS->>DB: 查询模块相关资源的review_status

    alt 用户选择发布
        DB->>WPS: review_status = 'approved'/'auto_approved'
        WPS->>A: 返回允许发布
        A->>WP: 创建作品广场记录
        WP->>DB: 保存到p_work_plaza表
        WP->>A: 返回发布成功
        A->>P: 通知发布成功
    else 用户选择不发布
        P->>A: 跳过发布，仅本地保存
        A->>P: 确认完成，无需发布
    end
```

#### Py视频创作工具业务流程C-9: 环境切换机制流程（核心机制）
```mermaid
sequenceDiagram
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant Config as 配置系统
    participant Mock as 模拟服务
    participant Real as 真实AI服务

    A->>SC: 请求AI服务调用
    SC->>Config: 读取AI_SERVICE_MODE配置

    alt 开发环境 (mock模式)
        Config->>SC: 返回mode=mock
        SC->>Mock: 调用模拟服务
        Mock->>SC: 返回模拟结果+mode=mock
        SC->>A: 返回结果{success:true, mode:'mock', data:...}
        Note over SC: 开发环境：无真实费用，快速响应
    else 生产环境 (real模式)
        Config->>SC: 返回mode=real
        SC->>Real: 调用真实AI服务
        Real->>SC: 返回真实结果
        SC->>A: 返回结果{success:true, mode:'real', data:...}
        Note over SC: 生产环境：真实调用，产生费用
    end

    Note over SC: 环境切换对业务层透明<br/>统一的调用接口和响应格式
```



## 🌐 WEB网页工具业务流程图

### 🌐 WEB网页工具核心业务流程

#### WEB网页工具1: 用户注册登录流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    W->>A: 用户注册请求(用户名/邮箱/密码)
    A->>DB: 检查用户名/邮箱是否存在
    alt 用户已存在
        DB->>A: 返回用户已存在
        A->>W: 返回注册失败(用户已存在)
    else 用户不存在
        A->>DB: 创建新用户记录
        A->>TP: 发送验证邮件/短信
        TP->>A: 返回发送结果
        A->>R: 缓存验证码
        A->>W: 返回注册成功(待验证)

        Note over W: 用户点击验证链接
        W->>A: 验证请求(验证码)
        A->>R: 验证验证码
        A->>DB: 激活用户账户
        A->>W: 返回验证成功

        W->>A: 登录请求(用户名/密码)
        A->>DB: 验证用户凭据
        A->>R: 生成并存储Token
        A->>W: 返回登录成功(Token)
    end
```

#### WEB网页工具2: 作品广场浏览流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    W->>A: 请求作品列表(分类/搜索条件)
    A->>R: 检查缓存中的作品列表
    alt 缓存命中
        R->>A: 返回缓存的作品列表
    else 缓存未命中
        A->>DB: 查询作品数据(p_work_plaza)
        DB->>A: 返回作品列表
        A->>R: 更新缓存
    end
    A->>W: 返回作品列表(含缩略图)

    Note over W: 用户点击查看作品详情
    W->>A: 请求作品详情(work_id)
    A->>DB: 查询作品详细信息
    A->>DB: 更新作品浏览次数
    A->>W: 返回作品详情(含高清图/视频)
```

#### WEB网页工具3: 用户中心管理流程
```mermaid
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方支付

    W->>A: 请求用户信息(Token)
    A->>R: 验证Token有效性
    A->>DB: 查询用户详细信息
    A->>W: 返回用户信息(积分/等级/统计)

    Note over W: 用户充值积分
    W->>A: 发起充值请求(金额)
    A->>TP: 调用支付接口
    TP->>A: 返回支付链接
    A->>W: 返回支付链接

    Note over W: 用户完成支付
    TP->>A: 支付回调通知
    A->>DB: 更新用户积分
    A->>R: 更新积分缓存
    A->>W: 推送充值成功通知
```

## 🏢 管理后台业务流程图

### 🏢 管理后台核心业务流程

#### 管理后台业务流程1: 系统配置管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant AI as AI平台配置

    M->>A: 管理员登录(用户名/密码)
    A->>DB: 验证管理员权限
    A->>R: 生成管理员Token
    A->>M: 返回登录成功(AdminToken)

    M->>A: 请求AI平台配置列表
    A->>DB: 查询AI平台配置
    A->>M: 返回配置列表(API密钥/地址)

    Note over M: 管理员修改AI平台配置
    M->>A: 更新AI平台配置(平台/密钥/地址)
    A->>AI: 测试新配置连通性
    alt 配置测试成功
        A->>DB: 保存新配置
        A->>R: 更新配置缓存
        A->>M: 返回配置更新成功
    else 配置测试失败
        A->>M: 返回配置测试失败(错误详情)
    end
```

#### 管理后台业务流程2: 用户管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    M->>A: 请求用户列表(分页/筛选条件)
    A->>DB: 查询用户数据(p_users)
    A->>M: 返回用户列表(基本信息/状态)

    Note over M: 管理员查看用户详情
    M->>A: 请求用户详情(user_id)
    A->>DB: 查询用户详细信息
    A->>DB: 查询用户积分记录
    A->>DB: 查询用户作品统计
    A->>M: 返回用户完整信息

    Note over M: 管理员操作用户账户
    M->>A: 用户账户操作(禁用/启用/积分调整)
    A->>DB: 更新用户状态/积分
    A->>R: 更新用户缓存
    alt 账户被禁用
        A->>R: 清除用户Token
    end
    A->>M: 返回操作成功
```

#### 管理后台业务流程3: 内容审核管理流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant U as 用户通知

    M->>A: 请求待审核内容列表
    A->>DB: 查询待审核作品(p_work_plaza)
    A->>M: 返回待审核列表(作品信息/提交时间)

    Note over M: 管理员审核作品
    M->>A: 请求作品详情(work_id)
    A->>DB: 查询作品完整信息
    A->>M: 返回作品详情(内容/资源/用户信息)

    M->>A: 提交审核结果(通过/拒绝/原因)
    A->>DB: 更新作品审核状态
    alt 审核通过
        A->>DB: 发布作品到广场
        A->>R: 更新作品缓存
        A->>U: 发送审核通过通知
    else 审核拒绝
        A->>U: 发送审核拒绝通知(含原因)
    end
    A->>M: 返回审核操作成功
```

#### 管理后台业务流程4: 数据统计分析流程
```mermaid
sequenceDiagram
    participant M as 管理后台
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    M->>A: 请求系统统计数据(时间范围)
    A->>R: 检查统计数据缓存
    alt 缓存命中且未过期
        R->>A: 返回缓存的统计数据
    else 缓存未命中或已过期
        A->>DB: 查询用户统计数据
        A->>DB: 查询收入统计数据
        A->>DB: 查询AI使用统计
        A->>DB: 查询作品发布统计
        A->>A: 汇总分析统计数据
        A->>R: 缓存统计结果(30分钟)
    end
    A->>M: 返回统计报表(图表数据)

    Note over M: 管理员导出报表
    M->>A: 请求导出报表(格式/范围)
    A->>A: 生成报表文件(Excel/PDF)
    A->>M: 返回报表下载链接
```

## 📊 图表使用说明

### 📋 图表分类说明

本文档包含以下类型的图表：

1. **系统架构图表**
   - 完整系统架构图（环境切换优化版）
   - AI服务集成模拟机制架构图
   - AI服务调用流程对比图
   - 项目依赖关系图

2. **Py视频创作工具业务流程图**
   - 用户管理业务流程（A-1到A-5：注册、登录、Token验证、密码管理）
   - 业务功能流程（B-1到B-5：充值积分、积分管理、代理推广、代理结算、数据处理）
   - AI核心流程（D-1：视频创作项目创建流程（纯文本数据处理版））
   - AI核心流程（D-2：AI分镜剧本生成与场景分镜数据入库流程（增强版））
   - AI核心流程（D-3：绑定角色流程（标准化版））
   - AI处理流程规范（C-1到C-9：AI任务调度、AI生成成功、积分不足、处理失败、超时中断、资源管理、资源下载、作品发布、环境切换）

3. **WEB网页工具业务流程图**
   - WEB网页工具1：用户注册登录流程
   - WEB网页工具2：作品广场浏览流程
   - WEB网页工具3：用户中心管理流程

4. **管理后台业务流程图**
   - 系统配置管理流程
   - 用户管理流程
   - 内容审核管理流程
   - 数据统计分析流程

### 🎯 图表使用指南

- 所有图表均使用 Mermaid 语法编写
- 图表支持在支持 Mermaid 的环境中直接渲染
- 每个图表都包含详细的流程说明和关键节点标注
- 图表按照业务逻辑分组，便于查找和使用
- 环境切换相关的图表特别标注了 mock/real 模式的区别

### 🔄 图表更新说明

本图表集合从 index-new.mdc 迁移而来，包含了完整的系统架构和业务流程设计。如需更新图表，请同时更新此文档以保持一致性。

## 📋 核心组件功能职责规范索引

### 🎯 AI程序员行为规范化指南

为增强AI程序员对系统架构的理解和行为规范化，以下是各核心组件的功能职责规范索引，详细内容请参考 **[index-new.mdc](.cursor/rules/index-new.mdc)**：

#### 🔧 工具API接口服务功能职责矩阵

**1. Py视频创作工具API接口职责** (`@php/api/app/Http/Controllers/PyApi`)
- ✅ **核心AI功能**: 创建AI创作视频任务、AI任务调度（文生文、图生图、图生视频、语音、音效、音乐）
- ✅ **用户管理**: 用户注册登录、资料修改、密码找回、用户认证
- ✅ **积分系统**: 充值积分、积分管理、积分明细
- ✅ **代理系统**: 代理推广、代理结算
- ✅ **数据处理**: 数据处理、作品发布
- ✅ **实时通信**: WebSocket服务（仅限Py视频创作工具）
- ❌ **禁止职责**: 不储存且不中转用户创作过程中AI生成的资源、视频编辑处理、客户端UI逻辑、本地文件操作

**2. WEB网页工具API接口职责** (`@php/api/app/Http/Controllers/WebApi`)
- ✅ **展示功能**: 功能介绍查询、价格方案查询、作品数据查询（分类筛选、搜索查看、作品详情展示）
- ✅ **用户中心**: 用户注册登录、资料修改、密码找回、用户认证、充值积分、积分管理、积分明细
- ✅ **代理功能**: 代理推广、代理结算
- ✅ **响应式设计**: 支持PC端、移动端、Py视频创作工具嵌入（1200px/800px窗口）
- ❌ **严格禁止**: 视频创作功能、AI生成功能、WebSocket实时通信、作品发布创建

**3. 管理后台API接口职责** (`@php/api/app/Http/Controllers/AdminApi`)
- ✅ **系统配置**: AI平台配置、系统参数设置
- ✅ **用户管理**: 用户信息、权限管理、账户状态
- ✅ **内容管理**: 作品审核、内容监控、违规处理
- ✅ **数据统计**: 用户统计、收入统计、使用情况分析
- ✅ **积分系统**: 积分规则、充值记录、消费明细
- ✅ **代理系统**: 代理审核、佣金结算、推广数据
- ✅ **素材库管理**: 音色库、音效库、音乐库、风格库、角色库
- ✅ **系统监控**: 性能监控、错误日志、API调用统计
- ✅ **财务管理**: 收入报表、退款处理、财务对账

#### 🚨 关键架构边界规范

**资源下载架构铁律**:
- ✅ **Py视频创作工具**: 必须直接从AI平台下载资源到本地
- ❌ **API服务器**: 严禁进行资源文件的中转下载、生成、处理、存储
- ✅ **服务器职责**: 仅负责管理资源的URL、状态、元数据等附件信息

**WebSocket使用边界**:
- ✅ **仅Py视频创作工具使用**: AI生成进度推送、任务状态通知
- ❌ **WEB工具禁用**: 避免不必要的连接和资源消耗
- 🔒 **安全传输**: 密钥加密传输，不持久化存储

#### 📊 WEB网页工具功能边界详细规范

**✅ 允许的功能职责**:
1. **展示职责**: 首页工具展示、功能介绍、价格方案展示、平台统计数据展示、公告和帮助信息
2. **用户中心**: 用户注册登录认证、资料管理修改、密码找回安全设置、积分查询充值明细
3. **作品广场**: 作品展示浏览、分类筛选搜索、作品详情查看、作品互动（点赞、分享）
4. **代理推广**: 代理申请管理、推广统计数据、佣金查询结算、推广链接生成
5. **响应式设计**: PC端完整功能体验、移动端优化适配、Py视频创作工具嵌入适配

**❌ 禁止的功能职责**:
1. **创作功能禁止**: 视频创作编辑、AI内容生成、素材处理合成、本地文件操作
2. **实时通信禁止**: WebSocket连接、实时进度推送、即时消息通信、长连接维护
3. **作品发布禁止**: 作品创建上传、作品发布到广场、作品审核管理、作品版本控制
4. **高级管理禁止**: 系统配置管理、用户权限管理、数据统计分析、内容审核操作

### 🎯 AI程序员开发指导原则

1. **严格遵循职责边界**: 每个组件只能执行其明确定义的职责范围内的功能
2. **禁止跨界操作**: 不得在组件中实现其禁止职责列表中的任何功能
3. **架构铁律优先**: 资源下载铁律和WebSocket使用边界不可违反
4. **环境切换机制**: 所有AI和第三方服务调用必须支持mock/real模式切换
5. **性能指标遵循**: 并发支持1000用户，响应时间符合规定标准

### 📚 详细规范文档参考

- **完整功能职责定义**: [index-new.mdc - 🎯 核心组件职责定义](.cursor/rules/index-new.mdc#核心组件职责定义)
- **WEB工具API接口列表**: [index-new.mdc - 📋 WEB网页工具API接口列表](.cursor/rules/index-new.mdc#web网页工具api接口列表)
- **架构边界规范**: [index-new.mdc - 🚨 关键架构原则](.cursor/rules/index-new.mdc#关键架构原则)
- **开发文档使用指南**: [index-new.mdc - 📚 开发文档使用指南](.cursor/rules/index-new.mdc#开发文档使用指南)

## 📝 文档维护说明

### 📋 文档定位与作用

本文档是 **AI视频创作工具系统图表集合** 的核心规划文档，包含完整的系统架构图、业务流程图和技术架构图。所有开发工作都应严格遵循本文档的图表规范进行系统设计和业务流程实现。如需修改请严格按原文档格式进行，必须先更新本文档并经过团队评审。

### 🎯 文档核心价值

1. **系统架构可视化**：通过 Mermaid 图表直观展示系统各组件间的关系和数据流向
2. **业务流程标准化**：详细定义各业务场景的标准流程，确保开发实现的一致性
3. **环境切换机制图解**：清晰展示 mock/real 模式的切换逻辑和实现方式
4. **开发协作指南**：为团队提供统一的架构理解和开发规范参考

### 🔧 文档使用规范

#### **开发前必读**
- 新功能开发前，必须先查阅相关的系统架构图和业务流程图
- 确保新功能的设计符合现有的架构边界和业务流程规范
- 涉及多组件交互的功能，必须参考完整系统架构图进行设计

#### **图表应用场景**
- **系统设计阶段**：参考系统架构图表进行组件设计和接口定义
- **业务开发阶段**：严格按照业务流程图实现各业务场景的处理逻辑
- **问题排查阶段**：通过流程图快速定位问题环节和数据流向
- **代码审查阶段**：对照图表检查实现是否符合设计规范

#### **环境切换开发指导**
- 所有AI服务调用必须参考"AI服务调用流程对比图"实现环境切换
- 第三方服务集成必须遵循"AI服务集成模拟机制架构图"的设计模式
- 开发和生产环境的切换逻辑必须与"环境切换机制流程"保持一致

### 📊 图表维护规范

#### **图表更新原则**
1. **架构变更优先**：系统架构发生变化时，必须优先更新相关架构图
2. **流程同步更新**：业务流程调整时，同步更新对应的业务流程图
3. **版本控制管理**：重大图表更新需要记录版本变更和更新原因
4. **团队评审机制**：图表更新必须经过技术团队评审确认

#### **图表质量标准**
- **准确性**：图表内容必须与实际系统实现保持一致
- **完整性**：关键业务流程和系统交互必须完整覆盖
- **清晰性**：图表结构清晰，标注详细，易于理解
- **一致性**：图表风格和命名规范保持统一

### 🚨 重要维护提醒

#### **修改前置条件**
- 任何架构设计修改都必须先更新本文档中的相关图表
- 图表更新必须经过团队技术评审和架构师确认
- 重大架构变更需要同时更新 `index-new.mdc` 中的相关规范

#### **影响性分析要求**
- 修改系统架构图前，必须分析对现有业务流程的影响
- 调整业务流程图前，必须评估对系统架构和接口设计的影响
- 环境切换相关的图表修改需要特别关注对开发和生产环境的影响

#### **文档同步机制**
- 本文档更新后，需要同步检查 `index-new.mdc` 中的相关内容
- 涉及API接口变更的图表更新，需要同步更新对应的API规范文档
- 数据库相关的流程图更新，需要同步检查 `dev-api-guidelines-database.mdc`

### 📈 文档版本信息

**当前版本**: v2.1 - 完整图表集合版
**最后更新**: 2025-08-05
**维护团队**: 架构设计组 + 开发团队
**更新频率**: 根据系统架构和业务需求变化及时更新

### 🔄 版本更新记录

**v2.1 更新内容**：
- ✅ 完善文档维护说明，增加详细的使用规范和维护标准
- ✅ 明确图表应用场景和开发指导原则
- ✅ 建立图表质量标准和更新机制
- ✅ 强化环境切换机制的图表指导作用

**v2.0 更新内容**：
- ✅ 从 `index-new.mdc` 迁移完整的图表集合
- ✅ 包含4类图表：系统架构、Py视频创作工具业务流程、WEB网页工具业务流程、管理后台业务流程
- ✅ 新增环境切换机制的详细图表说明
- ✅ 建立核心组件功能职责规范索引

### 📞 技术支持

如有图表理解或使用问题，请联系：
- **架构设计问题**：架构师团队
- **业务流程问题**：产品设计团队
- **技术实现问题**：开发团队负责人
- **文档维护问题**：技术文档维护组